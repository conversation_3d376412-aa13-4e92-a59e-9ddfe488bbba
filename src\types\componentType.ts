import type { Rule } from "@form-create/element-ui";

export interface AttributeTransferRuleDto {
  currentAttribute?: string;
  subAttribute?: string;
  transferRule?: 0 | 1 | 2;
  fixedValue?: any;
  calculationFormula?: string;
}

export interface FlowNodeTemplateArgDesDto {
  name?: string;
  type?: number;
  description?: string;
}

// 条件类型
export type ConditionType =
  | "equal"
  | "greater"
  | "less"
  | "notEqual"
  | "contains"
  | "notContains"
  | "regex";

// 子条件类型
export type SubConditionType = "and" | "or";

export interface RenderConditionDto {
  attributeName?: string;
  condition?: ConditionType;
  value?: any;
  valueType?: string;
  renderResource?: string;
  subConditions?: Array<
    Omit<RenderConditionDto, "subConditions" | "renderResource"> & {
      type: SubConditionType;
    }
  >;
}

export interface ComponentArgDto {
  name: string;
  paramType: string;
  valueType: number;
  value: string;
}

export interface ComponentFlowNodeInputArgDto {
  templateArgName?: string;
  valueName?: string;
}

export interface ComponentFlowNodeDto {
  id?: string;
  description?: string;
  templateName?: string;
  basicConfig: ComponentArgDto[];
  nextNodeIds?: string[];
  previousNodeIds?: string[];
}

export interface ComponentFunctionDto {
  name?: string;
  description?: string;
  inputArgs?: FlowNodeTemplateArgDesDto[];
  outputArgs?: FlowNodeTemplateArgDesDto[];
  flowNodes?: ComponentFlowNodeDto[];
  flowData: any;
}

export interface ComponentBasicInfoDto {
  // guid?: string;
  id?: string;
  name?: string;
  version?: string;
  versionDescription?: string;
  bindComponentName?: string;
  description?: string;
  tags?: string[];
  /** @format date-time */
  createTime?: string;
  author?: string;
}

export interface ComponentInfoDto {
  basicInfo?: ComponentBasicInfoDto;
  attributes?: ComponentAttributeDto[];
  subcomponents?: SubcomponentInfoDto[];
  alarms?: ComponentAlarmDto[];
  functions?: ComponentFunctionDto[];
  renderCondition?: RenderConditionDto[];
}

export interface ComponentListItem {
  id: string;
  name: string;
  tags: string[];
  version: string;
  createTime?: string;
  author?: string;
  description?: string;
  isFolder?: boolean; // 是否为文件夹
  children?: ComponentListItem[];
}

// 组件查询（通过ID）
export interface ComponentQueryWithIDDto {
  /** 组件ID */
  id: string;
}

// 组件查询（通过名称）
export interface ComponentQueryWithNameDto {
  /** 组件名称 */
  name: string;
}

// 属性限制规则DTO
export interface AttributeLimitRuleDto {
  /** 上限 */
  upperLimit?: string;
  /** 下限 */
  lowerLimit?: string;
  /** 长度限制 */
  lengthLimit?: string;
  /** 正则表达式 */
  regex?: string;
  /** 小数位数 */
  decimalDigits?: number;
}

// 组件属性DTO
export interface ComponentAttributeDto {
  /** 属性名称 */
  name?: string;
  /** 显示名称 */
  displayName?: string;
  /** 值类型，枚举值: 0-6 */
  type?: number;
  /** 描述 */
  description?: string;
  /** 单位 */
  unit?: string;
  /** 默认值 */
  defaultValue?: any;
  /** 是否持久化 */
  isPersistent?: boolean;
  /** 限制规则 */
  limitRule?: AttributeLimitRuleDto;

  // 兼容旧版属性
  valueType?: string;
  monitorInterval?: number;
  rules?: any;
}

// 组件告警DTO
export interface ComponentAlarmDto {
  /** 告警ID */
  index: number;
  /** 告警名称 */
  name: string;
  /** 告警描述 */
  description: string;
  /** 告警等级 */
  level: string;
  /** 可用处理方法 */
  processingMethod: string[];
}

// 功能参数
export interface FunctionInputArg {
  /** 参数名 */
  name: string;
  /** 参数类型 */
  type: number;
  /** 参数描述 */
  description: string;
}

// 功能固定参数
export interface FunctionFixedArg {
  /** 参数名 */
  name: string;
  /** 参数类型 */
  type: number;
  /** 参数值 */
  value: string;
}

// 功能输入参数
export interface FunctionInputArgMapping {
  /** 模板参数名 */
  templateArgName: string;
  /** 值名称 */
  valueName: string;
}

// 流程节点
export interface FlowNode {
  /** 节点ID */
  id: string;
  /** 节点描述 */
  description: string;
  /** 模板名称 */
  templateName: string;
  /** 固定参数 */
  fixedArgs: FunctionFixedArg[];
  /** 输入参数 */
  inputArgs: FunctionInputArgMapping[];
  /** 下一个节点ID列表 */
  nextNodeIds: string[];
  /** 上一个节点ID列表 */
  previousNodeIds: string[];
}

// 功能参数
export interface FunctionParameter {
  /** 参数名 */
  name: string;
  /** 参数类型 */
  type: string;
  /** 参数注释 */
  comment: string;
}

// 流程节点
export interface ProcessNode {
  /** 节点ID */
  nodeId: string;
  /** 节点备注 */
  nodeComment: string;
  /** 节点基础功能配置 */
  nodeBasicFunctionConfig: any;
  /** 上一个节点列表 */
  previousNodes: string[];
  /** 下一个节点列表 */
  nextNodes: string[];
}

// 子组件信息DTO
export interface SubcomponentInfoDto {
  /** 子组件名称 */
  name: string;
  /** 组件类型名称 */
  className: string;
  /** 组件类型版本 */
  classVersion: string;
  /** 参数传递规则 */
  inheritRule?: number;
  /** 属性传递规则列表 */
  attributeTransferRule?: AttributeTransferRuleDto[];
}

// 组件基本信息
export interface ComponentBasicInfo {
  /** 组件ID */
  id?: string;
  /** 兼容旧版的组件ID */
  guid?: string;
  /** 组件名称 */
  name: string;
  /** 版本 */
  version: string;

  /** 描述 */
  description: string;
  /** 版本描述 */
  versionDescription?: string;
  /** 标签 */
  tags: string[];
  /** 创建时间 */
  createTime: string;
  /** 作者 */
  author: string;
}

export enum NodeType {
  START = "Start",
  END = "End",
  CONDITION = "Condition",
  LOOP = "Loop",
  ALARM = "Alarm",
  COMPONENT_CONTROL = "ComponentControl",
  WRITE_VALUE = "WriteValue",
  READ_VALUE = "ReadValue",
  LISTEN_ATTRIBUTE = "ListenAttribute",
  LISTEN_PROPERTY = "ListenProperty",
  PROCESS_VALUE = "ProcessValue",
  GLOBAL_FUNCTION = "GlobalFunction",
  CUSTOM = "Custom"
}

export interface OutputParam {
  name: string;
  valueType: number;
  description?: string;
}

export interface WorkflowNodeData {
  name: string;
  name_cn: string;
  description: string;
  component: NodeType;
  color: string;
  rule: Rule[];
  initialValue: Record<string, any>;
  outputParams: OutputParam[];
  configValue: Record<string, any>;
  sourceHandleList?: string[];
}
