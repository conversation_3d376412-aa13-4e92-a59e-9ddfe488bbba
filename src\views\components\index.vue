<template>
  <div class="component-management-page">
    <div class="page-header">
      <div class="title-section">
        <div class="view-mode-switcher">
          <el-radio-group v-model="viewMode" size="default">
            <el-radio-button label="grouped">分组表格</el-radio-button>
            <el-radio-button label="table">表格</el-radio-button>
            <el-radio-button label="grid">网格</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <div class="action-section">
        <el-button type="primary" @click="handleCreateComponent">
          <el-icon><Plus /></el-icon>
          新建零件
        </el-button>
      </div>
    </div>

    <!-- 快速搜索栏 -->
    <div class="search-section">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索零件名称、标签或描述..."
        :prefix-icon="Search"
        clearable
        class="search-input"
        @keyup.enter="handleSearch"
      />
      <el-select
        v-model="typeFilter"
        placeholder="按标签筛选"
        clearable
        class="type-filter"
      >
        <el-option
          v-for="tag in availableTags"
          :key="tag"
          :label="tag"
          :value="tag"
        />
      </el-select>
    </div>

    <div class="page-content">
      <!-- 列表区域 -->
      <div class="list-panel">
        <div class="list-header">
          <span class="component-count"
            >共 {{ filteredComponents.length }} 个零件</span
          >
          <div class="list-actions">
            <el-button size="small" @click="refreshList">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </div>

        <component
          :is="currentViewComponent"
          :components="filteredComponents"
          @open="handleOpen"
          @copy="handleCopyFromTable"
          @delete="handleDeleteFromTable"
        />
      </div>
    </div>

    <!-- 创建/复制组件对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      :title="copyComponentId ? '复制零件' : '新建零件'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="newComponent"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="newComponent.name" placeholder="请输入零件名称" />
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <el-select
            v-model="newComponent.tags"
            multiple
            filterable
            allow-create
            placeholder="选择或创建标签"
            class="w-full"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="版本" prop="version">
          <el-input v-model="newComponent.version" placeholder="如：1.0.0" />
        </el-form-item>

        <el-form-item label="版本说明" prop="versionDescription">
          <el-input
            v-model="newComponent.versionDescription"
            type="textarea"
            :rows="2"
            placeholder="请输入版本说明"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="newComponent.description"
            type="textarea"
            :rows="3"
            placeholder="请输入零件描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreateSubmit">
          {{ copyComponentId ? "复制" : "创建" }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { Search, Plus, Refresh } from "@element-plus/icons-vue";

// 组件导入
import ComponentTable from "./components/ComponentTable.vue";
import ComponentGrid from "./components/ComponentGrid.vue";
import GroupedComponentTable from "./components/GroupedComponentTable.vue";

// Composables
import { useComponentList } from "./composables/useComponentList";
import { useComponentOperations } from "./composables/useComponentOperations";
import { useKeyboardShortcuts } from "./composables/useKeyboardShortcuts";
import { useComponentListStateHook } from "@/store/modules/componentListState";

defineOptions({
  name: "ComponentManagement"
});

const router = useRouter();

// 使用状态管理
const listState = useComponentListStateHook();

// 使用 composables
const {
  searchKeyword,
  typeFilter,
  availableTags,
  filteredComponents,
  refreshList
} = useComponentList();

const {
  handleCreateComponent,
  handleCopyComponent,
  handleDeleteComponent,
  createDialogVisible,
  newComponent,
  copyComponentId,
  formRules,
  resetForm,
  createComponent
} = useComponentOperations();

// 启用键盘快捷键
useKeyboardShortcuts({
  onCreateNew: handleCreateComponent,
  onFocusSearch: () => {
    const searchInput = document.querySelector(
      ".search-input input"
    ) as HTMLInputElement;
    searchInput?.focus();
  },

  onRefresh: refreshList,
  onToggleView: () => {
    const modes: ("grouped" | "table" | "grid")[] = [
      "grouped",
      "table",
      "grid"
    ];
    const currentIndex = modes.indexOf(viewMode.value);
    viewMode.value = modes[(currentIndex + 1) % modes.length];
  },
  onShowFilters: () => {
    // 高级筛选功能已移除
  }
});

// 视图模式 - 从状态管理获取
const viewMode = computed({
  get: () => listState.viewMode,
  set: (value: "grouped" | "table" | "grid") => listState.setViewMode(value)
});

// 当前视图组件
const currentViewComponent = computed(() => {
  switch (viewMode.value) {
    case "grouped":
      return GroupedComponentTable;
    case "table":
      return ComponentTable;
    case "grid":
      return ComponentGrid;
    default:
      return GroupedComponentTable;
  }
});

// 事件处理
const handleOpen = (component: any) => {
  router.push(`/components/${component.id}`);
};

const handleSearch = () => {
  // 搜索逻辑已在 composable 中处理
};

// 表单引用
const formRef = ref();

// 处理创建/复制提交
const handleCreateSubmit = async () => {
  try {
    await formRef.value?.validate();
    const success = await createComponent(newComponent.value, refreshList);
    if (success) {
      createDialogVisible.value = false;
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

// 处理表格中的复制操作
const handleCopyFromTable = (component: any) => {
  handleCopyComponent(component.id);
};

// 处理表格中的删除操作
const handleDeleteFromTable = (component: any) => {
  handleDeleteComponent(component.id);
};

// 初始化
onMounted(() => {
  // 恢复列表页状态
  listState.restoreFromStorage();

  // 初始化模拟数据到 localStorage（如果不存在）
  const existingData = localStorage.getItem("componentList");
  if (!existingData) {
    const mockData = [
      {
        id: 1,
        name: "Robot_A",
        tags: ["Robot", "Automation"],
        version: "1.0.0",
        createTime: "2024-01-15T10:30:00Z",
        author: "张三",
        description: "自动化机器人组件，用于物料搬运",
        isFolder: false
      },
      {
        id: 2,
        name: "LoadPort_Main",
        tags: ["LoadPort", "Interface"],
        version: "2.1.0",
        createTime: "2024-01-20T14:20:00Z",
        author: "李四",
        description: "主要装载端口组件",
        isFolder: false
      },
      {
        id: 3,
        name: "Buffer_Storage",
        tags: ["Buffer", "Storage"],
        version: "1.5.2",
        createTime: "2024-01-25T09:15:00Z",
        author: "王五",
        description: "缓存存储组件，提供临时存储功能",
        isFolder: false
      },
      {
        id: 4,
        name: "Chamber_Process",
        tags: ["Chamber", "Process"],
        version: "3.0.1",
        createTime: "2024-02-01T16:45:00Z",
        author: "赵六",
        description: "工艺腔体组件，执行主要工艺流程",
        isFolder: false
      },
      {
        id: 5,
        name: "EFEM_Controller",
        tags: ["EFEM", "Control"],
        version: "2.3.0",
        createTime: "2024-02-05T11:30:00Z",
        author: "钱七",
        description: "设备前端模块控制器",
        isFolder: false
      }
    ];

    localStorage.setItem("componentList", JSON.stringify(mockData));
  }

  // 刷新列表
  refreshList();
});
</script>

<style scoped lang="scss">
.component-management-page {
  height: calc(100vh - 76px);
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;

  .title-section {
    display: flex;
    align-items: center;
    gap: 24px;

    h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }
  }

  .action-section {
    display: flex;
    gap: 12px;
  }
}

.search-section {
  display: flex;
  gap: 12px;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;

  .search-input {
    flex: 1;
    max-width: 400px;
  }

  .type-filter {
    width: 200px;
  }
}

.page-content {
  flex: 1;
  display: flex;
  overflow: hidden;

  .list-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    margin: 16px 24px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;

  .component-count {
    font-size: 14px;
    color: #606266;
  }
}

.w-full {
  width: 100%;
}
</style>
