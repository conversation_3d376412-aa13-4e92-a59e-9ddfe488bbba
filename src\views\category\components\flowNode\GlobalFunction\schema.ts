import type { Api } from "@form-create/element-ui";
import {
  getAllComponent,
  getComponentFunctionsByName
} from "@/api/componentType";
import type {
  ComponentFunctionDto,
  FlowNodeTemplateArgDesDto
} from "@/types/componentType";

/**
 * 初始化项目中所有零件列表
 * @param setData 设置数据的函数
 * @param getData 获取数据的函数
 */
const initAllComponents = async (setData: any, getData: any) => {
  if (!getData("allComponents")) {
    try {
      const response = await getAllComponent();
      const components = response.map(comp => ({
        value: comp.basicInfo.name,
        label: comp.basicInfo.name,
        version: comp.basicInfo.version,
        className: comp.basicInfo.name
      }));
      setData("allComponents", components);
    } catch (error) {
      console.error("获取零件列表失败:", error);
      setData("allComponents", []);
    }
  }
};

/**
 * 加载零件功能列表
 * @param formApi 表单API
 * @param componentName 零件名称
 * @returns 功能选项列表
 */
const loadComponentFunctions = async (formApi: any, componentName: any) => {
  if (!componentName) return { options: [], functionsMap: {} };

  try {
    if (
      typeof componentName === "object" &&
      componentName.paramType === "ref"
    ) {
      return { options: [], functionsMap: {} };
    }

    const allComponents = formApi.getData("allComponents");
    const component = allComponents.find((c: any) => c.value === componentName);

    if (!component) {
      return { options: [], functionsMap: {} };
    }

    const { className, version } = component;
    const response = await getComponentFunctionsByName(className, version);

    if (!response.length) {
      return { options: [], functionsMap: {} };
    }

    // 创建函数映射，用于快速查找函数详情
    const functionsMap: Record<string, ComponentFunctionDto> = {};
    response.forEach((func: ComponentFunctionDto) => {
      functionsMap[func.name] = func;
    });

    const options = response.map((func: ComponentFunctionDto) => ({
      label: `${func.name}(${func.description || "无描述"})`,
      value: func.name
    }));

    return { options, functionsMap };
  } catch (error) {
    console.error("加载零件功能列表失败:", error);
    return { options: [], functionsMap: {} };
  }
};

/**
 * 根据函数参数动态生成表单项
 * @param inputArgs 函数参数列表
 * @returns 表单项规则
 */
const generateParamFormItems = (inputArgs: FlowNodeTemplateArgDesDto[]) => {
  if (!inputArgs || !Array.isArray(inputArgs) || inputArgs.length === 0) {
    return [];
  }

  const result = [];

  for (let i = inputArgs.length - 1; i >= 0; i--) {
    const arg = inputArgs[i];
    if (!arg.name) continue; // 跳过没有名称的参数

    let formItem: any = {
      field: `param__${arg.name}`,
      title: arg.description || arg.name,
      value: "",
      valueType: arg.type || 0,
      type: "input",
      props: {
        placeholder: `请输入${arg.description || arg.name}`,
        title: arg.name
      },
      validate: [{ required: true, message: `请输入${arg.name}` }]
    };

    // 根据类型选择不同的表单控件
    switch (arg.type) {
      case 0: // 字符串
      case 3: // 假设3也是字符串类型
        formItem.type = "input";
        break;
      case 1: // 数字
        formItem.type = "number";
        break;
      case 2: // 浮点数
        formItem.type = "number";
        formItem.props = {
          ...formItem.props,
          precision: 2
        };
        break;
      case 4: // 布尔值
        formItem.type = "switch";
        break;
      default:
        formItem.type = "input";
    }
    result.push(formItem);
  }

  return [
    ...result,
    {
      type: "el-divider",
      props: {
        contentPosition: "center"
      },
      children: ["函数参数"]
    }
  ];
};

export default [
  {
    type: "select",
    field: "ComponentName",
    title: "零件名称",
    value: "",
    valueType: 3,
    props: {
      filterable: true,
      clearable: true,
      placeholder: "请选择零件名称"
    },
    validate: [{ required: true, message: "请选择零件名称" }],
    col: { span: 24 },
    effect: {
      loadData: {
        // 数据名
        attr: "allComponents",
        // 插入位置
        to: "options"
      }
    },
    inject: true,
    on: {
      async change(inject: Api, value: any) {
        const { setValue, updateRule, removeField } = inject.$f;
        setValue("FunctionName", "");

        // 清除可能存在的参数字段
        const formData = inject.$f.form;
        Object.keys(formData).forEach(key => {
          if (key.startsWith("param_")) {
            removeField(key);
          }
        });

        const { options } = await loadComponentFunctions(inject.$f, value);
        updateRule("FunctionName", { options });
      }
    },
    hook: {
      async mounted(inject: any) {
        const { getValue, updateRule, setData, getData } = inject.$f;
        await initAllComponents(setData, getData);
        const componentName = getValue("ComponentName");

        // 如果有初始值，加载对应的功能列表
        if (componentName) {
          const { options } = await loadComponentFunctions(
            inject.$f,
            componentName
          );
          updateRule("FunctionName", { options });

          // 如果已选择了功能，加载对应的参数
          const functionName = getValue("FunctionName");
          if (functionName) {
            // 这里可以触发 FunctionName 的 change 事件
            // inject.$f.trigger("FunctionName", "change", functionName);
          }
        }
      }
    }
  },
  {
    type: "select",
    field: "FunctionName",
    title: "功能函数名",
    value: "",
    valueType: 3,
    col: { span: 24 },
    props: {
      filterable: true,
      clearable: true,
      placeholder: "请选择功能函数"
    },
    validate: [{ required: true, message: "请选择功能函数" }],
    options: [],
    inject: true,
    on: {
      async change(inject: Api, value: any) {
        const { removeField, refresh } = inject.$f;

        // 清除之前可能存在的参数字段
        const formData = inject.$f.form;
        Object.keys(formData).forEach(key => {
          if (key.startsWith("param__")) {
            removeField(key);
          }
        });

        if (!value) return;

        const componentName = formData.ComponentName;
        if (!componentName) return;

        // 加载函数列表和映射
        const { functionsMap } = await loadComponentFunctions(
          inject.$f,
          componentName
        );
        const selectedFunc = functionsMap[value];

        if (
          selectedFunc &&
          selectedFunc.inputArgs &&
          selectedFunc.inputArgs.length > 0
        ) {
          // 生成参数表单项
          const paramItems = generateParamFormItems(selectedFunc.inputArgs);
          // 动态添加参数表单项
          paramItems.forEach(item => {
            inject.$f.append(item);
          });

          // 刷新表单
          refresh();
        }
      }
    },
    hook: {
      async mounted(inject: any) {
        const { getValue, updateRule, setValue } = inject.$f;
        const componentName = getValue("ComponentName");

        // 如果有初始值，加载对应的功能列表
        if (componentName) {
          const { options, functionsMap } = await loadComponentFunctions(
            inject.$f,
            componentName
          );
          updateRule("FunctionName", { options });

          // 如果已选择了功能，加载对应的参数
          const functionName = getValue("FunctionName");
          if (functionName && functionsMap[functionName]) {
            // 获取选中功能的定义
            const selectedFunc = functionsMap[functionName];

            // 检查是否有输入参数需要注册
            if (
              selectedFunc &&
              selectedFunc.inputArgs &&
              selectedFunc.inputArgs.length > 0
            ) {
              const formData = inject.$f.form;
              // 生成参数表单项
              const paramItems = generateParamFormItems(selectedFunc.inputArgs);

              // 动态添加参数表单项
              paramItems.forEach(item => {
                inject.$f.append(item);

                // 检查是否有已保存的参数值需要回填
                const paramKey = item.field;
                if (formData[paramKey] !== undefined) {
                  // 如果已有值，设置到新添加的表单项中
                  setValue(paramKey, formData[paramKey]);
                }
              });

              // 刷新表单
              inject.$f.refresh();
            }
          }
        }
      }
    }
  }
];
