<template>
  <div class="node-toolbox">
    <h3 class="toolbox-title">通用功能</h3>

    <div class="node-list">
      <div
        v-for="node in nodeList"
        :key="node.name"
        class="node-item"
        draggable="true"
        @dragstart="onDragStart($event, node)"
      >
        <div
          class="node-type-indicator"
          :style="{ backgroundColor: node.color }"
        />
        <span class="node-label">{{ node.name_cn }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { ref } from "vue";
import Start from "../flowNode/Start";
import ComponentControl from "../flowNode/ComponentControl";
import End from "../flowNode/End";
import WriteValue from "../flowNode/WriteValue";
import ReadValue from "../flowNode/ReadValue";
import Condition from "../flowNode/Condition";
import Loop from "../flowNode/Loop";
import ProcessValue from "../flowNode/ProcessValue";
import GlobalFunction from "../flowNode/GlobalFunction";

const nodeList = [
  Start,
  ComponentControl,
  GlobalFunction,
  End,
  WriteValue,
  ReadValue,
  ProcessValue,
  Condition,
  Loop
];

// 拖拽开始
const onDragStart = (event: DragEvent, node: any) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData("application/vueflow", JSON.stringify(node));
    event.dataTransfer.effectAllowed = "move";
  }
};
</script>

<style scoped>
.node-toolbox {
  height: 100%;
  padding: 16px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-right: 1px solid #e4e7ed;
}

.toolbox-title {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
}

.node-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: grab;
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: all 0.3s;
}

.node-item:hover {
  background-color: #f5f7fa;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.node-type-indicator {
  width: 12px;
  height: 12px;
  margin-right: 8px;
  border-radius: 50%;
}

.node-label {
  font-size: 14px;
}

/* 节点类型样式 */
.node-type-start {
  background-color: #67c23a;
}

.node-type-end {
  background-color: #f56c6c;
}

.node-type-loop {
  background-color: #e6a23c;
}

.node-type-alarm {
  background-color: #f56c6c;
}

.node-type-component_control {
  background-color: #409eff;
}

.node-type-write_value {
  background-color: #909399;
}

.node-type-read_value {
  background-color: #67c23a;
}

.node-type-listen_attribute {
  background-color: #9254de;
}
</style>
