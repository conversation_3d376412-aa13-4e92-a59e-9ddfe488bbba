import { defineStore } from "pinia";
import { storageLocal } from "../utils";

// 列表页状态接口
interface ComponentListState {
  searchKeyword: string;
  typeFilter: string;
  viewMode: "grouped" | "table" | "grid";
  sortBy: string;
  sortOrder: "asc" | "desc";
}

// 默认状态
const defaultState: ComponentListState = {
  searchKeyword: "",
  typeFilter: "",
  viewMode: "grouped",
  sortBy: "name",
  sortOrder: "asc"
};

// localStorage 键名
const STORAGE_KEY = "component-list-state";

export const useComponentListState = defineStore("componentListState", {
  state: (): ComponentListState => {
    // 从 localStorage 恢复状态，如果不存在则使用默认状态
    const savedState = storageLocal().getItem<ComponentListState>(STORAGE_KEY);
    return savedState
      ? { ...defaultState, ...savedState }
      : { ...defaultState };
  },

  getters: {
    // 获取当前搜索关键词
    getSearchKeyword: state => state.searchKeyword,

    // 获取当前标签筛选
    getTypeFilter: state => state.typeFilter,

    // 获取当前视图模式
    getViewMode: state => state.viewMode,

    // 获取当前排序字段
    getSortBy: state => state.sortBy,

    // 获取当前排序顺序
    getSortOrder: state => state.sortOrder,

    // 检查是否有活跃的筛选条件
    hasActiveFilters: state => {
      return !!(state.searchKeyword || state.typeFilter);
    }
  },

  actions: {
    // 设置搜索关键词
    setSearchKeyword(keyword: string) {
      this.searchKeyword = keyword;
      this.saveToStorage();
    },

    // 设置标签筛选
    setTypeFilter(filter: string) {
      this.typeFilter = filter;
      this.saveToStorage();
    },

    // 设置视图模式
    setViewMode(mode: "grouped" | "table" | "grid") {
      this.viewMode = mode;
      this.saveToStorage();
    },

    // 设置排序
    setSorting(field: string, order: "asc" | "desc" = "asc") {
      this.sortBy = field;
      this.sortOrder = order;
      this.saveToStorage();
    },

    // 清除所有筛选条件
    clearFilters() {
      this.searchKeyword = "";
      this.typeFilter = "";
      this.saveToStorage();
    },

    // 重置所有状态到默认值
    resetState() {
      Object.assign(this, defaultState);
      this.saveToStorage();
    },

    // 批量更新状态
    updateState(partialState: Partial<ComponentListState>) {
      Object.assign(this, partialState);
      this.saveToStorage();
    },

    // 保存状态到 localStorage
    saveToStorage() {
      try {
        const stateToSave: ComponentListState = {
          searchKeyword: this.searchKeyword,
          typeFilter: this.typeFilter,
          viewMode: this.viewMode,
          sortBy: this.sortBy,
          sortOrder: this.sortOrder
        };
        storageLocal().setItem(STORAGE_KEY, stateToSave);
      } catch (error) {
        console.warn("保存列表页状态失败:", error);
      }
    },

    // 从 localStorage 恢复状态
    restoreFromStorage() {
      try {
        const savedState =
          storageLocal().getItem<ComponentListState>(STORAGE_KEY);
        if (savedState) {
          Object.assign(this, { ...defaultState, ...savedState });
        }
      } catch (error) {
        console.warn("恢复列表页状态失败:", error);
        Object.assign(this, defaultState);
      }
    }
  }
});

// 导出 store hook
export function useComponentListStateHook() {
  return useComponentListState();
}
