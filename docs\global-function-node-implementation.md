# 全局函数调用节点实现文档

## 概述

本文档描述了为工作流系统实现的全局函数调用节点的配置和功能。该节点允许用户在工作流中调用项目中任意零件的功能函数。

## 实现的功能

### 1. 节点参数配置

节点包含以下两个主要参数：

- **ComponentName (零件名称)**
  - 类型：String
  - 描述：零件名
  - 选择项：当前项目所有零件列表（从 `getAllComponent` API 获取）
  - 支持搜索和过滤

- **FunctionName (功能函数名)**
  - 类型：String  
  - 描述：功能函数名
  - 选择项：根据选中的零件动态加载其可用功能列表
  - 支持搜索和过滤

### 2. 动态参数生成

当用户选择了零件和功能函数后，系统会：

1. 自动获取该功能函数的输入参数定义
2. 根据参数类型动态生成对应的表单控件：
   - 字符串类型 → 文本输入框
   - 数字类型 → 数字输入框
   - 浮点数类型 → 带精度的数字输入框
   - 布尔类型 → 开关控件
3. 为每个参数添加必填验证
4. 支持参数值的保存和回显

## 技术实现

### 1. 文件结构

```
src/views/category/components/flowNode/GlobalFunction/
├── index.ts          # 节点基本信息配置
└── schema.ts         # 节点表单配置和逻辑
```

### 2. 核心功能函数

#### `initAllComponents`
- 功能：初始化项目中所有零件列表
- 数据源：`getAllComponent` API
- 返回格式：`{ value, label, version, className }`

#### `loadComponentFunctions`
- 功能：根据零件名称加载其功能列表
- 数据源：`getComponentFunctionsByName` API
- 返回：功能选项列表和功能映射对象

#### `generateParamFormItems`
- 功能：根据功能参数定义动态生成表单项
- 支持类型：字符串、数字、浮点数、布尔值
- 自动添加验证规则

### 3. 类型安全

- 使用 TypeScript 严格类型检查
- 导入相关类型定义：`ComponentFunctionDto`、`FlowNodeTemplateArgDesDto`
- 处理可选属性的类型安全

## 集成配置

### 1. 节点类型注册

在 `src/types/componentType.ts` 中添加：
```typescript
export enum NodeType {
  // ... 其他类型
  GLOBAL_FUNCTION = "GlobalFunction",
  // ...
}
```

### 2. 节点配置注册

在 `src/views/category/components/flowNode/index.ts` 中：
- 导入 schema 和配置
- 注册到 `nodeSchemas` 和 `nodeConfigInfo`

### 3. 工具箱集成

在 `src/views/category/components/workflow/NodeToolbox.vue` 中：
- 导入 GlobalFunction 节点
- 添加到节点列表中

## 使用方式

1. 用户从工具箱拖拽"全局函数调用"节点到工作流画布
2. 双击节点打开配置面板
3. 选择目标零件名称（支持搜索）
4. 选择要调用的功能函数（支持搜索）
5. 填写函数所需的输入参数
6. 保存配置

## 特性

- ✅ 动态加载项目零件列表
- ✅ 级联选择：零件 → 功能函数
- ✅ 动态参数表单生成
- ✅ 类型安全的参数输入
- ✅ 参数验证
- ✅ 配置保存和回显
- ✅ 搜索和过滤支持

## 与 ComponentControl 节点的区别

| 特性 | ComponentControl | GlobalFunction |
|------|------------------|----------------|
| 零件范围 | 当前零件的子零件 | 项目中所有零件 |
| 数据源 | componentStore.getSubcomponents | getAllComponent API |
| 使用场景 | 控制子零件 | 调用任意零件功能 |

## 后续扩展

可以考虑的功能扩展：
- 支持输出参数配置
- 添加函数调用结果处理
- 支持异步函数调用
- 添加错误处理配置
