import { http } from "@/utils/http";

import type {
  ComponentAttributeDto,
  ComponentInfoDto
} from "@/types/componentType";

const BASE_URL = "http://192.168.0.212:6888";

/**
 * 根据ID获取单个组件详细信息
 * @param id 组件ID
 * @returns 组件详细信息
 */
export function getComponentByID(id: string) {
  return http.post<ComponentInfoDto, { id: string }>(
    `${BASE_URL}/Component/GetComponentByID`,
    { data: { id } }
  );
}

/**
 * 更新组件信息
 * @param component 完整的组件信息数据传输对象
 * @returns 操作结果
 */
export function updateComponent(component: ComponentInfoDto) {
  return http.post<any, ComponentInfoDto>(
    `${BASE_URL}/Component/UpdateComponent`,
    { data: component }
  );
}

/**
 * 根据名称获取组件功能列表
 * @param name 组件名称
 * @returns 组件功能列表
 */
export function getComponentFunctionsByName(name: string, version: string) {
  return http.post<any, { name: string; version: string }>(
    `${BASE_URL}/Component/GetFunctionsByName`,
    { data: { name, version } }
  );
}

/**
 * 获取全部组件信息
 * @returns 组件信息列表
 */
export function getAllComponent() {
  return http.get<ComponentInfoDto[], any>(
    `${BASE_URL}/Component/GetAllComponent`,
    {}
  );
}

/**
 * 添加新组件
 * @param component 完整的组件信息数据传输对象
 * @returns 操作结果
 */
export function addComponent(component: ComponentInfoDto) {
  return http.post<{ id: string }, ComponentInfoDto>(
    `${BASE_URL}/Component/AddComponent`,
    { data: component }
  );
}

/**
 * 根据ID获取组件属性
 * @param id 组件ID
 * @returns 组件属性列表
 */
export function getAttributesByID(id: string) {
  const query: { id: string } = { id };
  return http.post<ComponentAttributeDto[], { id: string }>(
    `${BASE_URL}/Component/GetAttributesByID`,
    { data: query }
  );
}
