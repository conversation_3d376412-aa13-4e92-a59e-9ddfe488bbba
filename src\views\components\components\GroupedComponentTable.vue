<template>
  <div class="grouped-component-table">
    <!-- 工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button size="small" :disabled="allExpanded" @click="expandAllRows">
          全部展开
        </el-button>
        <el-button
          size="small"
          :disabled="allCollapsed"
          @click="collapseAllRows"
        >
          全部折叠
        </el-button>
        <el-divider direction="vertical" />
        <span class="summary-info">
          共 {{ groupedData.totalTags }} 个分组，{{
            groupedData.totalComponents
          }}
          个组件
        </span>
      </div>
    </div>

    <!-- 主表格 -->
    <el-table
      ref="mainTableRef"
      :data="filteredGroupData"
      :height="tableHeight"
      row-key="id"
      width="100%"
      @expand-change="handleExpandChange"
      @sort-change="handleGroupSort"
    >
      <!-- 展开列 -->
      <el-table-column
        type="expand"
        width="50"
        :expand-row-keys="expandedRowKeys"
      >
        <template #default="{ row }">
          <div class="expanded-content">
            <!-- 展开行标题 -->
            <div class="expanded-header">
              <h4 class="group-title">
                <el-tag type="primary" size="large">{{ row.tagName }}</el-tag>
                <span class="group-subtitle">分组详情</span>
              </h4>
              <div class="group-stats">
                <el-statistic
                  title="组件数量"
                  :value="row.componentCount"
                  class="stat-item"
                />
                <div class="stat-item">
                  <div class="stat-title">最后更新</div>
                  <div class="stat-value">
                    {{ formatDate(row.lastUpdateTime) }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 组件子表格 -->
            <div class="component-sub-table">
              <ComponentTable
                :components="getFilteredComponents(row)"
                :compact-mode="true"
                custom-height="auto"
                @open="component => handleComponentOpen(component, row)"
                @copy="component => handleComponentCopy(component, row)"
                @delete="component => handleComponentDelete(component, row)"
                @sort-change="sort => handleComponentSort(sort, row)"
              />
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 分组名称列 -->
      <el-table-column
        prop="tagName"
        label="分组名称"
        width="200"
        sortable="custom"
      >
        <template #default="{ row }">
          <div class="group-name-cell">
            <el-tag type="primary" size="default">
              {{ row.tagName }}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <!-- 组件数量列 -->
      <el-table-column
        prop="componentCount"
        label="组件数量"
        width="120"
        sortable="custom"
        align="center"
      >
        <template #default="{ row }">
          <el-badge
            :value="row.componentCount"
            :max="99"
            class="component-count-badge"
          >
            <el-icon size="20"><Document /></el-icon>
          </el-badge>
        </template>
      </el-table-column>

      <!-- 最后更新时间列 -->
      <el-table-column
        prop="lastUpdateTime"
        label="最后更新"
        width="180"
        sortable="custom"
      >
        <template #default="{ row }">
          <div class="update-time-cell">
            <el-icon><Clock /></el-icon>
            <span>{{ formatDate(row.lastUpdateTime) }}</span>
          </div>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <div class="group-actions">
            <el-button
              size="small"
              type="primary"
              link
              @click="toggleRowExpansion(row)"
            >
              {{ isRowExpanded(row.id) ? "折叠" : "展开" }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElTable } from "element-plus";
import { Document, Search, Clock } from "@element-plus/icons-vue";
import ComponentTable from "./ComponentTable.vue";
import type { ComponentListItem } from "@/types/componentType";
import { formatDate } from "../utils/componentUtils";

// 分组数据结构
interface TagGroupRow {
  id: string;
  tagName: string;
  componentCount: number;
  components: ComponentListItem[];
  lastUpdateTime: string;
}

// 组件处理结果
interface GroupedTableData {
  groups: TagGroupRow[];
  totalComponents: number;
  totalTags: number;
  expandedRows: string[];
}

interface Props {
  components: ComponentListItem[];
}

interface Emits {
  (e: "open", component: ComponentListItem, group?: TagGroupRow): void;
  (e: "copy", component: ComponentListItem, group?: TagGroupRow): void;
  (e: "delete", component: ComponentListItem, group?: TagGroupRow): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 引用
const mainTableRef = ref<InstanceType<typeof ElTable>>();

// 响应式数据
const expandedRowKeys = ref<string[]>([]);
const groupSortConfig = ref({ prop: "", order: "" });

// 数据处理：将组件按 tag 分组
const processComponentsToGroups = (
  components: ComponentListItem[]
): TagGroupRow[] => {
  const tagGroups: Record<string, TagGroupRow> = {};

  components.forEach(component => {
    component.tags.forEach(tag => {
      if (!tagGroups[tag]) {
        tagGroups[tag] = {
          id: `group-${tag}`,
          tagName: tag,
          componentCount: 0,
          components: [],
          lastUpdateTime: ""
        };
      }

      const group = tagGroups[tag];

      // 添加组件（避免重复）
      if (!group.components.find(c => c.id === component.id)) {
        group.components.push(component);
        group.componentCount++;
      }

      // 更新最后更新时间
      if (component.createTime) {
        const componentTime = new Date(component.createTime);
        const groupTime = group.lastUpdateTime
          ? new Date(group.lastUpdateTime)
          : new Date(0);
        if (componentTime > groupTime) {
          group.lastUpdateTime = component.createTime;
        }
      }
    });
  });

  return Object.values(tagGroups);
};

// 分组数据
const groupedData = computed((): GroupedTableData => {
  const groups = processComponentsToGroups(props.components);

  return {
    groups,
    totalComponents: props.components.length,
    totalTags: groups.length,
    expandedRows: expandedRowKeys.value
  };
});

// 过滤后的分组数据
const filteredGroupData = computed(() => {
  let filtered = groupedData.value.groups;

  // 分组排序
  if (groupSortConfig.value.prop) {
    filtered = sortGroups(
      filtered,
      groupSortConfig.value.prop,
      groupSortConfig.value.order
    );
  }

  return filtered;
});

// 展开/折叠控制
const expandAllRows = () => {
  expandedRowKeys.value = filteredGroupData.value.map(group => group.id);
};

const collapseAllRows = () => {
  expandedRowKeys.value = [];
};

const toggleRowExpansion = (row: TagGroupRow) => {
  mainTableRef.value?.toggleRowExpansion(row);
};

const isRowExpanded = (rowId: string): boolean => {
  return expandedRowKeys.value.includes(rowId);
};

// 计算属性：全部展开/折叠状态
const allExpanded = computed(() => {
  return expandedRowKeys.value.length === filteredGroupData.value.length;
});

const allCollapsed = computed(() => {
  return expandedRowKeys.value.length === 0;
});

// 事件处理
const handleExpandChange = (row: TagGroupRow, expandedRows: TagGroupRow[]) => {
  expandedRowKeys.value = expandedRows.map(r => r.id);
};

const handleGroupSort = ({ prop, order }: { prop: string; order: string }) => {
  groupSortConfig.value = { prop, order };
};

// 组件事件处理
const handleComponentOpen = (
  component: ComponentListItem,
  group: TagGroupRow
) => {
  emit("open", component, group);
};

const handleComponentCopy = (
  component: ComponentListItem,
  group: TagGroupRow
) => {
  emit("copy", component, group);
};

const handleComponentDelete = (
  component: ComponentListItem,
  group: TagGroupRow
) => {
  emit("delete", component, group);
};

const handleComponentSort = (
  sort: { prop: string; order: string },
  group: TagGroupRow
) => {
  // 处理组件级别的排序
  // 可以在这里实现每个分组内的独立排序
};

// 工具函数
const sortGroups = (
  groups: TagGroupRow[],
  prop: string,
  order: string
): TagGroupRow[] => {
  return [...groups].sort((a, b) => {
    let aValue = a[prop as keyof TagGroupRow];
    let bValue = b[prop as keyof TagGroupRow];

    if (prop === "lastUpdateTime") {
      aValue = new Date(aValue as string).getTime();
      bValue = new Date(bValue as string).getTime();
    }

    if (typeof aValue === "string") {
      aValue = aValue.toLowerCase();
      bValue = (bValue as string).toLowerCase();
    }

    const result = aValue > bValue ? 1 : -1;
    return order === "ascending" ? result : -result;
  });
};

const getFilteredComponents = (group: TagGroupRow): ComponentListItem[] => {
  return group.components;
};

// 表格高度
const tableHeight = computed(() => "calc(100vh - 350px)");
</script>

<style scoped lang="scss">
.grouped-component-table {
  height: 100%;
  display: flex;
  flex-direction: column;

  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 16px;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .summary-info {
        font-size: 14px;
        color: #606266;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  :deep(.el-table) {
    // 主表格样式
    .group-name-cell {
      display: flex;
      align-items: center;
    }

    .component-count-badge {
      .el-badge__content {
        background-color: var(--el-color-primary);
        border: none;
      }
    }

    .update-time-cell {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      color: #606266;
    }

    .group-actions {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    // 展开行样式
    .el-table__expanded-cell {
      padding: 0;
      background-color: #fafbfc;
    }
  }

  .expanded-content {
    padding: 20px;

    .expanded-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e4e7ed;

      .group-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0;
        font-size: 16px;
        font-weight: 600;

        .group-subtitle {
          font-size: 14px;
          font-weight: normal;
          color: #909399;
        }
      }

      .group-stats {
        display: flex;
        gap: 24px;

        .stat-item {
          text-align: center;

          .stat-title {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }

          .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          :deep(.el-statistic__content) {
            font-size: 16px;
            font-weight: 600;
          }

          :deep(.el-statistic__head) {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .component-sub-table {
      background-color: #fff;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      :deep(.el-table) {
        border: none;

        .el-table__header {
          background-color: #f8f9fa;

          th {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e4e7ed;
          }
        }

        .el-table__body {
          tr:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .grouped-component-table {
    .expanded-content {
      .expanded-header {
        flex-direction: column;
        gap: 16px;

        .group-stats {
          justify-content: space-around;
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .grouped-component-table {
    .table-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .toolbar-left,
      .toolbar-right {
        justify-content: center;
      }
    }
  }
}
</style>
