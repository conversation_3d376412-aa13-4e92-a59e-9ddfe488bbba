import { NodeType } from "@/types/componentType";

import startSchema from "./Start/schema";
import endSchema from "./End/schema";
import conditionSchema from "./Condition/schema";
import LoopSchema from "./Loop/schema";
import componentControlSchema from "./ComponentControl/schema";
import writeValueSchema from "./WriteValue/schema";
import readValueSchema from "./ReadValue/schema";
import processValueSchema from "./ProcessValue/schema";
import globalFunctionSchema from "./GlobalFunction/schema";

import startData from "./Start/index";
import endData from "./End/index";
import conditionData from "./Condition/index";
import componentControlData from "./ComponentControl/index";
import writeValueData from "./WriteValue/index";
import readValueData from "./ReadValue/index";
import LoopData from "./Loop/index";
import processValueData from "./ProcessValue/index";
import globalFunctionData from "./GlobalFunction/index";

import conditionNodeConfigRender from "./Condition/nodeConfigRender";
import processValueNodeConfigRender from "./ProcessValue/nodeConfigRender";

export const nodeSchemas = {
  [NodeType.START]: startSchema,
  [NodeType.END]: endSchema,
  [NodeType.CONDITION]: conditionSchema,
  [NodeType.LOOP]: LoopSchema,
  [NodeType.COMPONENT_CONTROL]: componentControlSchema,
  [NodeType.WRITE_VALUE]: writeValueSchema,
  [NodeType.READ_VALUE]: readValueSchema,
  [NodeType.PROCESS_VALUE]: processValueSchema,
  [NodeType.GLOBAL_FUNCTION]: globalFunctionSchema
};

export const nodeConfigInfo = {
  [NodeType.START]: startData,
  [NodeType.END]: endData,
  [NodeType.CONDITION]: conditionData,
  [NodeType.LOOP]: LoopData,
  [NodeType.COMPONENT_CONTROL]: componentControlData,
  [NodeType.WRITE_VALUE]: writeValueData,
  [NodeType.READ_VALUE]: readValueData,
  [NodeType.PROCESS_VALUE]: processValueData,
  [NodeType.GLOBAL_FUNCTION]: globalFunctionData
};

export const nodeConfigRender = {
  [NodeType.CONDITION]: conditionNodeConfigRender,
  [NodeType.PROCESS_VALUE]: processValueNodeConfigRender
};

export function getNodeSchema(nodeName: string) {
  return nodeSchemas[nodeName] || [];
}
